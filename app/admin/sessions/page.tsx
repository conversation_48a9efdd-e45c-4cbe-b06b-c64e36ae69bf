'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { toast } from 'sonner'
import { Trash2, RefreshCw, Users, Monitor, Clock, Shield, AlertTriangle } from 'lucide-react'
import { useAdminPermission } from '@/hooks/use-admin-permission'
import { useAdminSession } from '@/hooks/use-admin-session'

interface SessionSummary {
  sessionId: string
  userId: number
  userName: string
  role: 'student' | 'admin' | 'super_admin'
  deviceId: string
  ipAddress: string
  deviceType: string
  browser: string
  createdAt: string
  lastAccessedAt: string
  expiresAt: string
  isActive: boolean
}

interface SessionStats {
  totalSessions: number
  activeSessions: number
  expiredSessions: number
  sessionsByRole: Record<string, number>
}

interface SessionFilter {
  role?: string
  isActive?: string
  userId?: string
  limit: number
  offset: number
}

export default function SessionManagementPage() {
  const { permission, loading: permissionLoading } = useAdminPermission('super_admin')
  const { admin } = useAdminSession() // Get current admin info
  const [sessions, setSessions] = useState<SessionSummary[]>([])
  const [stats, setStats] = useState<SessionStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<SessionFilter>({
    limit: 50,
    offset: 0,
    isActive: 'true', // Default to show only active sessions
  })
  const [totalCount, setTotalCount] = useState(0)
  const [searchUserId, setSearchUserId] = useState('')

  // Fetch sessions
  const fetchSessions = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()

      if (filter.role) params.append('role', filter.role)
      if (filter.isActive) params.append('isActive', filter.isActive)
      if (filter.userId) params.append('userId', filter.userId)
      params.append('limit', filter.limit.toString())
      params.append('offset', filter.offset.toString())

      const response = await fetch(`/api/admin/sessions?${params}`)

      if (!response.ok) {
        if (response.status === 500) {
          // Handle session system not fully initialized
          console.warn('Session management system not fully initialized')
          setSessions([])
          setTotalCount(0)
          toast.error('Session management system is initializing. Please try again in a moment.')
          return
        }
        throw new Error('Failed to fetch sessions')
      }

      const data = await response.json()
      setSessions(data.data.sessions)
      setTotalCount(data.data.pagination.total)
    } catch (error) {
      console.error('Error fetching sessions:', error)
      toast.error('Failed to fetch sessions')
      setSessions([])
      setTotalCount(0)
    } finally {
      setLoading(false)
    }
  }

  // Fetch session statistics
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/sessions/stats')

      if (!response.ok) {
        if (response.status === 500) {
          // Handle session system not fully initialized
          console.warn('Session statistics not available yet')
          setStats({
            totalSessions: 0,
            activeSessions: 0,
            expiredSessions: 0,
            sessionsByRole: {},
          })
          return
        }
        throw new Error('Failed to fetch session stats')
      }

      const data = await response.json()
      setStats(data.data)
    } catch (error) {
      console.error('Error fetching session stats:', error)
      toast.error('Failed to fetch session statistics')
      setStats({
        totalSessions: 0,
        activeSessions: 0,
        expiredSessions: 0,
        sessionsByRole: {},
      })
    }
  }

  // Invalidate a specific session
  const invalidateSession = async (sessionId: string) => {
    try {
      const response = await fetch('/api/admin/sessions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId }),
      })

      if (!response.ok) {
        // Get the error message from the response
        let errorMessage = 'Failed to invalidate session'
        try {
          const errorData = await response.json()
          errorMessage = errorData.error || errorMessage
        } catch (parseError) {
          console.error('Error parsing error response:', parseError)
        }

        // Show specific error message for self-invalidation attempt
        if (errorMessage.includes('Cannot invalidate your own sessions')) {
          toast.error(
            'Security Error: You cannot invalidate your own session. This prevents accidental self-logout.'
          )
        } else {
          toast.error(`Error: ${errorMessage}`)
        }
        return
      }

      toast.success('Session invalidated successfully')
      fetchSessions()
      fetchStats()
    } catch (error) {
      console.error('Error invalidating session:', error)
      toast.error(
        'Network error: Failed to connect to server. Please check your connection and try again.'
      )
    }
  }

  // Force logout a user
  const forceLogoutUser = async (userId: number) => {
    try {
      const response = await fetch('/api/admin/sessions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      })

      if (!response.ok) {
        // Get the error message from the response
        let errorMessage = 'Failed to force logout user'
        try {
          const errorData = await response.json()
          errorMessage = errorData.error || errorMessage
        } catch (parseError) {
          console.error('Error parsing error response:', parseError)
        }

        // Show specific error message for self-logout attempt
        if (
          errorMessage.includes('Cannot invalidate your own sessions') ||
          errorMessage.includes('Cannot force logout yourself')
        ) {
          toast.error(
            'Security Error: You cannot force logout yourself. This is a security measure to prevent accidental self-logout.'
          )
        } else {
          toast.error(`Error: ${errorMessage}`)
        }
        return
      }

      const data = await response.json()
      toast.success(`${data.invalidatedCount} session(s) invalidated successfully`)

      // Trigger immediate session check for affected users
      // This will help force logout to be more immediate
      setTimeout(() => {
        fetchSessions()
        fetchStats()
      }, 1000) // Small delay to allow session invalidation to propagate
    } catch (error) {
      console.error('Error forcing logout:', error)
      toast.error(
        'Network error: Failed to connect to server. Please check your connection and try again.'
      )
    }
  }

  // Handle search
  const handleSearch = () => {
    setFilter(prev => ({
      ...prev,
      userId: searchUserId || undefined,
      offset: 0,
    }))
  }

  // Handle filter change
  const handleFilterChange = (key: keyof SessionFilter, value: string) => {
    setFilter(prev => ({
      ...prev,
      [key]: value || undefined,
      offset: 0,
    }))
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  // Get role badge color
  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'bg-red-100 text-red-800'
      case 'admin':
        return 'bg-blue-100 text-blue-800'
      case 'student':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Check if session is near expiry
  const isNearExpiry = (expiresAt: string) => {
    const expiry = new Date(expiresAt)
    const now = new Date()
    const fiveMinutes = 5 * 60 * 1000
    return expiry.getTime() - now.getTime() < fiveMinutes
  }

  // Check if session belongs to current admin
  const isCurrentUserSession = (session: SessionSummary) => {
    return admin && session.userId === admin.id
  }

  useEffect(() => {
    if (!permissionLoading && permission?.isSuperAdmin) {
      fetchSessions()
      fetchStats()
    }
  }, [permissionLoading, permission?.isSuperAdmin, filter])

  if (permissionLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!permission?.isSuperAdmin) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="w-96">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Access Denied
            </CardTitle>
            <CardDescription>
              You need super admin privileges to access session management.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Session Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage user sessions across the system
          </p>
        </div>
        <Button
          onClick={() => {
            fetchSessions()
            fetchStats()
          }}
          disabled={loading}
        >
          <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Session Statistics */}
      {stats && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalSessions}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
              <Monitor className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.activeSessions}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Expired Sessions</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.expiredSessions}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">By Role</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                {Object.entries(stats.sessionsByRole).map(([role, count]) => (
                  <div key={role} className="flex justify-between text-sm">
                    <span className="capitalize">{role}</span>
                    <span className="font-medium">{count}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <div>
              <label className="text-sm font-medium">User ID</label>
              <div className="flex gap-2">
                <Input
                  placeholder="Enter user ID"
                  value={searchUserId}
                  onChange={e => setSearchUserId(e.target.value)}
                />
                <Button onClick={handleSearch}>Search</Button>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium">Role</label>
              <Select
                value={filter.role || 'all'}
                onValueChange={value => handleFilterChange('role', value === 'all' ? '' : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All roles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All roles</SelectItem>
                  <SelectItem value="student">Student</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="super_admin">Super Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Status</label>
              <Select
                value={filter.isActive || 'true'}
                onValueChange={value =>
                  handleFilterChange('isActive', value === 'all' ? '' : value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Active sessions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="true">Active only</SelectItem>
                  <SelectItem value="false">Inactive only</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Limit</label>
              <Select
                value={filter.limit.toString()}
                onValueChange={value => handleFilterChange('limit', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sessions Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            {filter.isActive === 'true'
              ? 'Active Sessions'
              : filter.isActive === 'false'
                ? 'Inactive Sessions'
                : 'All Sessions'}{' '}
            ({totalCount})
          </CardTitle>
          <CardDescription>
            {sessions.length === 0 && !loading ? (
              <span className="text-amber-600">
                Session management system is initializing. New sessions will appear here once users
                login with the enhanced system.
              </span>
            ) : (
              `Showing ${sessions.length} of ${totalCount} sessions`
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
            </div>
          ) : sessions.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Shield className="mb-4 h-12 w-12 text-gray-400" />
              <h3 className="mb-2 text-lg font-medium text-gray-900">No Sessions Found</h3>
              <p className="max-w-md text-gray-500">
                The session management system is ready but no enhanced sessions are active yet.
                Sessions will appear here when users login with the new session management system.
              </p>
              <p className="mt-2 text-sm text-gray-400">
                Current admin sessions using legacy tokens will not appear in this list.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Device</TableHead>
                    <TableHead>IP Address</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Last Access</TableHead>
                    <TableHead>Expires</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sessions.map(session => {
                    const isOwnSession = isCurrentUserSession(session)
                    return (
                      <TableRow
                        key={session.sessionId}
                        className={isOwnSession ? 'bg-blue-50 dark:bg-blue-950/20' : ''}
                      >
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div>
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{session.userName}</span>
                                {isOwnSession && (
                                  <Badge
                                    variant="outline"
                                    className="border-blue-300 bg-blue-100 text-xs text-blue-800"
                                  >
                                    You
                                  </Badge>
                                )}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                ID: {session.userId}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getRoleBadgeColor(session.role)}>
                            {session.role.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="text-sm">{session.deviceType}</div>
                            <div className="text-xs text-muted-foreground">{session.browser}</div>
                          </div>
                        </TableCell>
                        <TableCell className="font-mono text-sm">{session.ipAddress}</TableCell>
                        <TableCell className="text-sm">{formatDate(session.createdAt)}</TableCell>
                        <TableCell className="text-sm">
                          {formatDate(session.lastAccessedAt)}
                        </TableCell>
                        <TableCell>
                          <div
                            className={`text-sm ${isNearExpiry(session.expiresAt) ? 'font-medium text-red-600' : ''}`}
                          >
                            {formatDate(session.expiresAt)}
                            {isNearExpiry(session.expiresAt) && (
                              <div className="text-xs text-red-500">Expiring soon</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={session.isActive ? 'default' : 'secondary'}>
                            {session.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="outline" size="sm">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Invalidate Session</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    {isOwnSession ? (
                                      <div className="space-y-2">
                                        <div className="flex items-center gap-2 text-amber-600">
                                          <AlertTriangle className="h-4 w-4" />
                                          <span className="font-medium">
                                            Warning: This is your own session!
                                          </span>
                                        </div>
                                        <p>
                                          You cannot invalidate your own session as this would log
                                          you out and is blocked for security reasons.
                                        </p>
                                      </div>
                                    ) : (
                                      <>
                                        Are you sure you want to invalidate this session? The user
                                        will be logged out immediately.
                                      </>
                                    )}
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => invalidateSession(session.sessionId)}
                                    disabled={!!isOwnSession}
                                  >
                                    Invalidate
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>

                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="destructive" size="sm">
                                  Force Logout
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Force Logout User</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    {isOwnSession ? (
                                      <div className="space-y-2">
                                        <div className="flex items-center gap-2 text-amber-600">
                                          <AlertTriangle className="h-4 w-4" />
                                          <span className="font-medium">
                                            Warning: You cannot logout yourself!
                                          </span>
                                        </div>
                                        <p>
                                          You cannot force logout your own account as this is
                                          blocked for security reasons to prevent accidental
                                          self-logout.
                                        </p>
                                      </div>
                                    ) : (
                                      <>
                                        Are you sure you want to force logout {session.userName}?
                                        This will invalidate all their active sessions.
                                      </>
                                    )}
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => forceLogoutUser(session.userId)}
                                    disabled={!!isOwnSession}
                                  >
                                    Force Logout
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalCount > filter.limit && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {filter.offset + 1} to {Math.min(filter.offset + filter.limit, totalCount)}{' '}
                of {totalCount} sessions
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={filter.offset === 0}
                  onClick={() =>
                    setFilter(prev => ({ ...prev, offset: Math.max(0, prev.offset - prev.limit) }))
                  }
                >
                  Previous
                </Button>

                {/* Page numbers */}
                <div className="flex items-center gap-1">
                  {(() => {
                    const currentPage = Math.floor(filter.offset / filter.limit) + 1
                    const totalPages = Math.ceil(totalCount / filter.limit)
                    const pages = []

                    // Show first page
                    if (currentPage > 3) {
                      pages.push(
                        <Button
                          key={1}
                          variant={1 === currentPage ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setFilter(prev => ({ ...prev, offset: 0 }))}
                        >
                          1
                        </Button>
                      )
                      if (currentPage > 4) {
                        pages.push(
                          <span key="ellipsis1" className="px-2">
                            ...
                          </span>
                        )
                      }
                    }

                    // Show current page and surrounding pages
                    for (
                      let i = Math.max(1, currentPage - 2);
                      i <= Math.min(totalPages, currentPage + 2);
                      i++
                    ) {
                      pages.push(
                        <Button
                          key={i}
                          variant={i === currentPage ? 'default' : 'outline'}
                          size="sm"
                          onClick={() =>
                            setFilter(prev => ({ ...prev, offset: (i - 1) * prev.limit }))
                          }
                        >
                          {i}
                        </Button>
                      )
                    }

                    // Show last page
                    if (currentPage < totalPages - 2) {
                      if (currentPage < totalPages - 3) {
                        pages.push(
                          <span key="ellipsis2" className="px-2">
                            ...
                          </span>
                        )
                      }
                      pages.push(
                        <Button
                          key={totalPages}
                          variant={totalPages === currentPage ? 'default' : 'outline'}
                          size="sm"
                          onClick={() =>
                            setFilter(prev => ({ ...prev, offset: (totalPages - 1) * prev.limit }))
                          }
                        >
                          {totalPages}
                        </Button>
                      )
                    }

                    return pages
                  })()}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  disabled={filter.offset + filter.limit >= totalCount}
                  onClick={() => setFilter(prev => ({ ...prev, offset: prev.offset + prev.limit }))}
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
